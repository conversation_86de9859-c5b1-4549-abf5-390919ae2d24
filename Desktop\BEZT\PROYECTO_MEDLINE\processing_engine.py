# Archivo: processing_engine.py

import pandas as pd

def _get_final_pedimento(patente, pedimento, aduana, df_701):
    """
    Busca un pedimento en la tabla 701 para encontrar su versión final si fue rectificado.
    Devuelve la tupla (patente, pedimento, aduana) final.
    """
    # Convertir a string para asegurar la comparación
    pedimento = str(pedimento)
    patente = str(patente)
    aduana = str(aduana)

    # Filtrar por el pedimento original/anterior
    rect_row = df_701[
        (df_701['pedimentoanterior'] == pedimento) & 
        (df_701['patenteanterior'] == patente) &
        (df_701['seccionaduaneraanterior'] == aduana)
    ]
    
    if not rect_row.empty:
        # Se encontró una rectificación, ahora buscamos si esta rectificación fue rectificada a su vez (recursividad)
        new_patente = rect_row.iloc[0]['patente']
        new_pedimento = rect_row.iloc[0]['pedimento']
        new_aduana = rect_row.iloc[0]['seccionaduanera']
        # Llamada recursiva para encontrar la última versión
        return _get_final_pedimento(new_patente, new_pedimento, new_aduana, df_701)
    else:
        # No se encontraron más rectificaciones, esta es la versión final
        return patente, pedimento, aduana

def procesar_drawback(ds_dfs, client_df, log_callback):
    """
    El motor principal del sistema. Procesa cada fila del reporte del cliente,
    la valida contra el Data Stage, calcula el drawback y la clasifica.
    """
    log_callback("Iniciando motor de procesamiento...")

    # Extraer los DataFrames necesarios del diccionario del Data Stage
    df_551 = ds_dfs.get('551')
    df_701 = ds_dfs.get('701')

    if df_551 is None or df_701 is None:
        log_callback("❌ ERROR: Faltan tablas esenciales (551 o 701) en el Data Stage.")
        return [], []

    resultados_mismo_estado = []
    resultados_transformado = []
    
    total_rows = len(client_df)
    log_callback(f"Procesando {total_rows} registros del reporte del cliente...")

    # --- Bucle principal: iterar sobre cada descarga del cliente ---
    for index, row in client_df.iterrows():
        log_callback(f"\n--- Procesando registro {index + 1}/{total_rows} (Parte: {row['numeroparte']}) ---")
        
        try:
            # 1. VALIDACIÓN DE RECTIFICACIÓN (Pedimento de Importación)
            patente_impo_orig = row['patente_impo']
            pedimento_impo_orig = row['pedimento_impo']
            aduana_impo_orig = row['aduana_impo']
            
            final_patente, final_pedimento, final_aduana = _get_final_pedimento(
                patente_impo_orig, pedimento_impo_orig, aduana_impo_orig, df_701
            )
            
            if final_pedimento != pedimento_impo_orig:
                log_callback(f"⚠️ Pedimento de importación rectificado. Original: {pedimento_impo_orig}, Final: {final_pedimento}")
            
            # 2. ENCONTRAR LA PARTIDA CORRESPONDIENTE EN EL DATA STAGE (Tabla 551)
            secuencia_impo = str(row['secuencia_impo'])
            
            partida_ds = df_551[
                (df_551['patente'] == final_patente) &
                (df_551['pedimento'] == final_pedimento) &
                (df_551['seccionaduanera'] == final_aduana) &
                (df_551['secuenciafraccion'] == secuencia_impo)
            ]

            if partida_ds.empty:
                log_callback(f"❌ ERROR: No se encontró la partida en el Data Stage (Ped: {final_pedimento}, Sec: {secuencia_impo}). Se omite este registro.")
                continue
            if len(partida_ds) > 1:
                log_callback(f"❌ ERROR: Se encontraron múltiples ({len(partida_ds)}) partidas coincidentes en el Data Stage. Se omite este registro.")
                continue

            partida_ds_row = partida_ds.iloc[0]

            # 3. EXTRACCIÓN Y CÁLCULO DEL DRAWBACK
            # Convertir a numérico, manejando errores
            valor_aduana_total_ds = pd.to_numeric(partida_ds_row['valoraduana'], errors='coerce')
            cantidad_total_ds = pd.to_numeric(partida_ds_row['cantidadumtarifa'], errors='coerce')
            tasa_igi_cliente = pd.to_numeric(row['tasa_igi'], errors='coerce')
            cantidad_descargada_cliente = pd.to_numeric(row['cantidad_descargada'], errors='coerce')

            if pd.isna(valor_aduana_total_ds) or pd.isna(cantidad_total_ds) or pd.isna(tasa_igi_cliente) or pd.isna(cantidad_descargada_cliente):
                log_callback("❌ ERROR: Datos numéricos inválidos en el Data Stage o reporte del cliente. Se omite este registro.")
                continue
            
            if cantidad_total_ds == 0:
                log_callback("❌ ERROR: La cantidad total en la partida del Data Stage es 0. No se puede calcular el costo unitario.")
                continue

            # El cálculo proporcional que definimos
            costo_unitario_aduana = valor_aduana_total_ds / cantidad_total_ds
            valor_aduana_proporcional = costo_unitario_aduana * cantidad_descargada_cliente
            monto_drawback_calculado = valor_aduana_proporcional * (tasa_igi_cliente / 100)

            log_callback(f"Cálculo de Drawback: ${monto_drawback_calculado:.4f}")

            # 4. CONSTRUIR LA FILA DE SALIDA con el formato del SAT
            output_row = {
                # Mapeo de columnas basado en el layout del SAT que definimos
                'RFC del solicitante': "PME880419NU5", # Valor fijo de ejemplo
                'Año': pd.to_datetime(row['fechapago_expo']).year if pd.notna(row['fechapago_expo']) else '',
                'Folio': '', # Se puede generar un folio si es necesario
                'Factor de incorporación': row['factorincorporacion'],
                # Sección de Importación
                'Número del pedimento (Importación)': final_pedimento,
                'Pedimento rectificado (Importación)': "si" if final_pedimento != pedimento_impo_orig else "no",
                'Secuencia / Partida del pedimento (Importación)': secuencia_impo,
                'Fracción arancelaria (Importación)': partida_ds_row['fraccion'],
                'Descripción de la mercancía (Importación)': partida_ds_row['descripcionmercancia'],
                'País de origen': partida_ds_row['paisorigendestino'],
                'Medidas / Tamaño / Modelo (Importación)': partida_ds_row['modelomercanciaproducto'],
                'Cantidad solicitada (Importación)': cantidad_descargada_cliente,
                'Unidad de Medida Comercial (Importación)': partida_ds_row['unidadmedidacomercial'],
                'Precio unitario (Importación)': costo_unitario_aduana,
                'Número de factura (Importación)': row['factura_impo'],
                # Sección de Exportación
                'Número del pedimento (Exportación)': row['pedimento_expo'],
                'Fecha (Exportación)': row['fechapago_expo'],
                'Pedimento rectificado (Exportación)': "si" if pd.notna(row['pedim_orig_expo']) and row['pedim_orig_expo'] != 0 else "no",
                'Secuencia / Partida del pedimento (Exportación)': row['secuencia_expo'],
                'Fracción arancelaria (Exportación)': row['fraccion_expo'],
                'Descripción de la mercancía (Exportación)': row['descrip_expo'],
                'País de Destino': row['pais_destino'],
                'Medidas / Tamaño / Modelo (Exportación)': row['modelo'],
                'Cantidad solicitada (Exportación)': cantidad_descargada_cliente,
                'Unidad de Medida Comercial (Exportación)': row['umc_expo'],
                'Precio unitario (Exportación)': row['precio_unit_expo'],
                'Número de factura (Exportación)': row['factura_expo'],
                # Sección de Devolución
                'Monto a devolver en moneda nacional': monto_drawback_calculado,
                # Sección de Verificación
                'Número del pedimento (Verificación)': final_pedimento,
                'Secuencia / Partida del pedimento (Verificación)': secuencia_impo,
                'Fracción arancelaria (Verificación)': partida_ds_row['fraccion'],
                'Descripción de la mercancía (Verificación)': partida_ds_row['descripcionmercancia'],
                'Cantidad importada': cantidad_total_ds,
                'Monto del impuesto de importación pagado por la importacion definitiva': monto_drawback_calculado,
            }

            # 5. CLASIFICAR Y AÑADIR A LA LISTA CORRESPONDIENTE
            factor = pd.to_numeric(row['factorincorporacion'], errors='coerce')
            if pd.notna(factor) and factor > 0:
                resultados_transformado.append(output_row)
                log_callback("Clasificado como: TRANSFORMADO")
            else:
                resultados_mismo_estado.append(output_row)
                log_callback("Clasificado como: MISMO ESTADO")

        except Exception as e:
            log_callback(f"❌ ERROR INESPERADO en registro {index + 1}: {e}. Se omite este registro.")
            continue
            
    log_callback("\n✅ Motor de procesamiento finalizado.")
    return resultados_mismo_estado, resultados_transformado