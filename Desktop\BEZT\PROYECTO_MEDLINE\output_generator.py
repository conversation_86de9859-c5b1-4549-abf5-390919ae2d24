# Archivo: output_generator.py

import pandas as pd
import os
from datetime import datetime

# --- PLANTILLAS DE COLUMNAS PARA EL FORMATO DEL SAT ---
# Estas listas definen el orden y nombre exacto de las columnas en los archivos de salida.

# Renombramos las columnas de nuestro DataFrame interno a las que el SAT espera.
# Nota cómo manejamos los nombres duplicados.
COLUMN_MAPPING_SAT = {
    'Número del pedimento (Importación)': 'Número del pedimento',
    'Pedimento rectificado (Importación)': 'Pedimento rectificado',
    'Secuencia / Partida del pedimento (Importación)': 'Secuencia / Partida del pedimento',
    'Fracción arancelaria (Importación)': 'Fracción arancelaria',
    'Descripción de la mercancía (Importación)': 'Descripción de la mercancía',
    'Medidas / Tamaño / Modelo (Importación)': 'Medidas / Tama<PERSON> / Modelo',
    'Cantidad solicitada (Importación)': 'Cantidad solicitada',
    'Unidad de Medida Comercial (Importación)': 'Unidad de Medida Comercial',
    'Precio unitario (Importación)': 'Precio unitario',
    'Número de factura (Importación)': 'Número de factura',
    'Número del pedimento (Exportación)': 'Número del pedimento',
    'Fecha (Exportación)': 'Fecha',
    'Pedimento rectificado (Exportación)': 'Pedimento rectificado',
    'Secuencia / Partida del pedimento (Exportación)': 'Secuencia / Partida del pedimento',
    'Fracción arancelaria (Exportación)': 'Fracción arancelaria',
    'Descripción de la mercancía (Exportación)': 'Descripción de la mercancía',
    'Medidas / Tamaño / Modelo (Exportación)': 'Medidas / Tamaño / Modelo',
    'Cantidad solicitada (Exportación)': 'Cantidad solicitada',
    'Unidad de Medida Comercial (Exportación)': 'Unidad de Medida Comercial',
    'Precio unitario (Exportación)': 'Precio unitario',
    'Número de factura (Exportación)': 'Número de factura',
    'Número del pedimento (Verificación)': 'Número del pedimento',
    'Secuencia / Partida del pedimento (Verificación)': 'Secuencia / Partida del pedimento',
    'Fracción arancelaria (Verificación)': 'Fracción arancelaria',
    'Descripción de la mercancía (Verificación)': 'Descripción de la mercancía',
}


# Orden final de las columnas para el reporte de MISMO ESTADO
COLUMNS_MISMO_ESTADO = [
    'RFC del solicitante', 'Año', 'Folio', 
    # Importación
    'Número del pedimento', 'Pedimento rectificado', 'Secuencia / Partida del pedimento', 
    'Fracción arancelaria', 'Descripción de la mercancía', 'País de origen', 
    'Medidas / Tamaño / Modelo', 'Cantidad solicitada', 'Unidad de Medida Comercial', 
    'Precio unitario', 'Número de factura',
    # Exportación
    'Número del pedimento', 'Fecha', 'Pedimento rectificado', 
    'Secuencia / Partida del pedimento', 'Fracción arancelaria', 'Descripción de la mercancía', 
    'País de Destino', 'Medidas / Tamaño / Modelo', 'Cantidad solicitada', 
    'Unidad de Medida Comercial', 'Precio unitario', 'Número de factura',
    # Devolución y Verificación
    'Monto a devolver en moneda nacional', 'Número del pedimento', 'Secuencia / Partida del pedimento',
    'Fracción arancelaria', 'Descripción de la mercancía', 'Cantidad importada',
    'Monto del impuesto de importación pagado por la importacion definitiva'
]

# Orden final para el reporte de TRANSFORMADO (incluye Factor de Incorporación)
COLUMNS_TRANSFORMADO = [
    'RFC del solicitante', 'Año', 'Folio', 'Factor de incorporación',
    # El resto de las columnas son las mismas que en Mismo Estado
] + COLUMNS_MISMO_ESTADO[3:]


def _escribir_excel(df, base_filename, column_order, output_folder, log_callback):
    """Función auxiliar para escribir un DataFrame a un archivo Excel con formato específico."""
    if df.empty:
        log_callback(f"⚠️ Aviso: No hay datos para generar el archivo '{base_filename}'. Se omitirá.")
        return None

    # Renombrar columnas para que coincidan con los nombres duplicados del layout del SAT
    df_renamed = df.rename(columns=COLUMN_MAPPING_SAT)

    # Reordenar y seleccionar las columnas finales
    try:
        df_final = df_renamed[column_order]
    except KeyError as e:
        log_callback(f"❌ ERROR: Falta una columna esperada en los datos procesados para el archivo de salida: {e}")
        return None
    
    # Crear nombre de archivo con fecha
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    final_filename = f"{timestamp}_{base_filename}.xlsx"
    output_path = os.path.join(output_folder, final_filename)

    try:
        df_final.to_excel(output_path, index=False, engine='openpyxl')
        log_callback(f"✅ Archivo generado exitosamente: {final_filename}")
        return output_path
    except Exception as e:
        log_callback(f"❌ ERROR: No se pudo escribir el archivo Excel en '{output_path}'. Causa: {e}")
        return None

def generar_archivos_excel(df_mismo_estado, df_transformado, output_folder, log_callback):
    """
    Toma los DataFrames procesados y genera los dos archivos Excel de salida.
    """
    log_callback("Iniciando generación de archivos Excel de salida...")
    
    created_files = []

    # Generar archivo para "Mismo Estado"
    path1 = _escribir_excel(
        df_mismo_estado,
        "DRAWBACK_MISMO_ESTADO",
        COLUMNS_MISMO_ESTADO,
        output_folder,
        log_callback
    )
    if path1:
        created_files.append(path1)

    # Generar archivo para "Transformado"
    path2 = _escribir_excel(
        df_transformado,
        "DRAWBACK_TRANSFORMADO",
        COLUMNS_TRANSFORMADO,
        output_folder,
        log_callback
    )
    if path2:
        created_files.append(path2)
        
    log_callback("✅ Generación de archivos finalizada.")
    return created_files