# Archivo: data_stage_loader.py (Versión Corregida)

import os
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from collections import defaultdict

def _formar_pedimento_datastage(patente, pedimento, seccion_aduanera, fecha_pago):
    """
    Forma el pedimento como aparece en el Data Stage concatenando:
    - Primeros 2 dígitos del año de fecha_pago
    - Primeros 2 dígitos de seccion_aduanera
    - Todos los dígitos de patente
    - Todos los dígitos del pedimento original

    Ejemplo: 2025-03-06, 240, 1488, 5010668 -> 252414885010668
    """
    try:
        # Extraer año de la fecha
        if pd.isna(fecha_pago) or fecha_pago == '':
            return None

        if isinstance(fecha_pago, str):
            fecha_obj = pd.to_datetime(fecha_pago, errors='coerce')
        else:
            fecha_obj = fecha_pago

        if pd.isna(fecha_obj):
            return None

        year_2digits = str(fecha_obj.year)[-2:]  # Últimos 2 dígitos del año

        # Primeros 2 dígitos de sección aduanera
        seccion_2digits = str(seccion_aduanera)[:2]

        # Formar el pedimento completo
        pedimento_formado = f"{year_2digits}{seccion_2digits}{patente}{pedimento}"

        return pedimento_formado
    except Exception:
        return None

def _agregar_pedimento_formado(df, tabla_key, log_callback):
    """
    Agrega la columna 'pedimento_formado' a las tablas que la necesitan (551 y 701).
    """
    if tabla_key not in ['551', '701']:
        return df

    if df is None or df.empty:
        return df

    # Verificar que las columnas necesarias existan
    required_cols = ['patente', 'pedimento', 'seccionaduanera', 'fechapagoreal']
    missing_cols = [col for col in required_cols if col not in df.columns]

    if missing_cols:
        log_callback(f"⚠️ Tabla {tabla_key}: Faltan columnas para formar pedimento: {missing_cols}")
        return df

    log_callback(f"Formando pedimentos para tabla {tabla_key}...")

    # Aplicar la función de formación de pedimento
    df['pedimento_formado'] = df.apply(
        lambda row: _formar_pedimento_datastage(
            row['patente'],
            row['pedimento'],
            row['seccionaduanera'],
            row['fechapagoreal']
        ),
        axis=1
    )

    # Contar cuántos pedimentos se formaron exitosamente
    pedimentos_formados = df['pedimento_formado'].notna().sum()
    total_registros = len(df)

    log_callback(f"✅ Tabla {tabla_key}: {pedimentos_formados}/{total_registros} pedimentos formados exitosamente")

    return df

# Las definiciones de estructura se mantienen igual
FILE_MAPPING = {
    '501': ('_501.asc', 'T501_Datos_Generales'), '505': ('_505.asc', 'T505_Facturas'),
    '506': ('_506.asc', 'T506_Fechas_Pedimento'), '507': ('_507.asc', 'T507_Casos_Pedimento'),
    '510': ('_510.asc', 'T510_Contribuciones'), '551': ('_551.asc', 'T551_Partidas'),
    '552': ('_552.asc', 'T552_Mercancias'), '553': ('_553.asc', 'T553_Permiso_Partida'),
    '554': ('_554.asc', 'T554_Casos_Partida'), '556': ('_556.asc', 'T556_Tasas_Contribuciones'),
    '557': ('_557.asc', 'T557_Identificadores_Partida'), '701': ('_701.asc', 'T701_Rectificaciones')
}

STANDARD_HEADERS = {
    '501': ["patente", "pedimento", "seccionaduanera", "tipooperacion", "clavedocumento", "seccionaduaneraentrada", "curpcontribuyente", "rfc", "curpagentea", "tipocambio", "totalfletes", "totalseguros", "totalembalajes", "totalincrementables", "totaldeducibles", "pesobrutomercancia", "mediotransportesalida", "mediotransportearribo", "mediotransporteentrada_salida", "destinomercancia", "nombrecontribuyente", "callecontribuyente", "numinteriorcontribuyente", "numexteriorcontribuyente", "cpcontribuyente", "municipiocontribuyente", "entidadfedcontribuyente", "paiscontribuyente", "tipopedimento", "fecharecepcionpedimento", "fechapagoreal"],
    '505': ["patente", "pedimento", "seccionaduanera", "fechafacturacion", "numerofactura", "terminofacturacion", "monedafacturacion", "valordolares", "valormonedaextranjera", "paisfacturacion", "entidadfedfacturacion", "indentfiscalproveedor", "proveedormercancia", "calleproveedor", "numinteriorproveedor", "numexteriorproveedor", "cpproveedor", "municipioproveedor", "fechapagoreal"],
    '506': ["patente", "pedimento", "seccionaduanera", "tipofecha", "fechaoperacion", "fechavalidacionpagor"],
    '507': ["patente", "pedimento", "seccionaduanera", "clavecaso", "identificadorcaso", "tipopedimento", "complementocaso", "fechavalidacionpagor"],
    '510': ["patente", "pedimento", "seccionaduanera", "clavecontribucion", "formapago", "importepago", "tipopedimento", "fechapagoreal"],
    '551': ["patente", "pedimento", "seccionaduanera", "fraccion", "secuenciafraccion", "subdivisionfraccion", "descripcionmercancia", "preciounitario", "valoraduana", "valorcomercial", "valordolares", "cantidadumcomercial", "unidadmedidacomercial", "cantidadumtarifa", "unidadmedidatarifa", "valoragregado", "clavevinculacion", "metodovalorizacion", "codigomercanciaproducto", "marcamercanciaproducto", "modelomercanciaproducto", "paisorigendestino", "paiscompradorvendedor", "entidadfedorigen", "entidadfeddestino", "entidadfedcomprador", "entidadfedvendedor", "tipooperacion", "clavedocumento", "fechapagoreal"],
    '552': ["patente", "pedimento", "seccionaduanera", "fraccion", "secuenciafraccion", "vinnumeroserie", "kilometrajevehiculo", "fechapagoreal"],
    '553': ["patente", "pedimento", "seccionaduanera", "fraccion", "secuenciafraccion", "clavepermiso", "firmadescargo", "numeropermiso", "valorcomercialdolares", "cantidadmumtarifa", "fechapagorea"],
    '554': ["patente", "pedimento", "seccionaduanera", "fraccion", "secuenciafraccion", "clavecaso", "identificadorcaso", "complementocaso", "fechapagoreal"],
    '556': ["patente", "pedimento", "seccionaduanera", "fraccion", "secuenciafraccion", "clavecontribucion", "tasacontribucion", "tipotasa", "fechapagoreal"],
    '557': ["patente", "pedimento", "seccionaduanera", "fraccion", "secuenciafraccion", "clavecontribucion", "formapago", "importepago", "fechapagoreal"],
    '701': ["patente", "pedimento", "seccionaduanera", "clavedocumento", "fechapago", "pedimentoanterior", "patenteanterior", "seccionaduaneraanterior", "documentoanterior", "fechaoperacionanterior", "pedimentooriginal", "patenteaduanalorig", "seccionaduaneradesporig", "fechapagoreal"]
}

def _process_single_file(file_info):
    """
    Procesa un solo archivo .asc de forma robusta, leyendo línea por línea
    para evitar errores de tokenizing.
    """
    file_path, key = file_info
    try:
        with open(file_path, 'r', encoding='latin1') as f:
            lines = [line for line in f if line.strip()] # Leer solo líneas no vacías

        if not lines:
            return key, None, "Archivo vacío"

        # Extraer y limpiar headers de la primera línea
        headers = [h.strip() for h in lines[0].strip().split('|')]
        
        # Corrección para el delimitador final: si la última columna del header está vacía, la eliminamos
        if headers and not headers[-1]:
            headers.pop()
            
        data_rows = []
        # Procesar líneas de datos (todas menos la primera)
        for line in lines[1:]:
            row = line.strip().split('|')
            
            # Corrección para el delimitador final en filas de datos
            if row and len(row) > len(headers):
                 if row[-1] == '':
                    row.pop()

            # Normalizar el número de columnas para que coincida con los headers
            if len(row) > len(headers):
                row = row[:len(headers)]  # Truncar columnas extra
            elif len(row) < len(headers):
                row.extend([''] * (len(headers) - len(row))) # Rellenar con vacíos

            data_rows.append(row)
        
        if not data_rows:
            return key, None, "No se encontraron datos válidos (solo encabezado)"

        # Crear el DataFrame desde los datos procesados manualmente
        df = pd.DataFrame(data_rows, columns=headers, dtype=str)
        
        # Asignar los nombres de columna estándar para consistencia
        if key in STANDARD_HEADERS:
            expected_headers = STANDARD_HEADERS[key]
            # Solo renombramos las columnas existentes, no añadimos ni quitamos
            df.columns = expected_headers[:len(df.columns)]
        
        return key, df, f"OK - {len(df)} filas"
    
    except Exception as e:
        return key, None, f"Error: {e}"

def cargar_data_stage(ruta_carpeta, log_callback):
    """
    Explora una carpeta, procesa todos los archivos .asc del Data Stage en paralelo
    y devuelve un diccionario de DataFrames.
    """
    log_callback("Iniciando carga del Data Stage (modo robusto)...")
    log_callback(f"Buscando archivos en: {ruta_carpeta}")

    files_to_process = []
    for dirpath, _, filenames in os.walk(ruta_carpeta):
        for filename in filenames:
            if filename.lower().endswith('.asc'):
                for key, (suffix, _) in FILE_MAPPING.items():
                    if filename.lower().endswith(suffix.lower()):
                        files_to_process.append((os.path.join(dirpath, filename), key))
                        break
    
    if not files_to_process:
        log_callback("⚠️ Aviso: No se encontraron archivos .asc válidos en la carpeta.")
        return None

    log_callback(f"Se encontraron {len(files_to_process)} archivos .asc para procesar.")
    
    all_dataframes = defaultdict(list)
    processed_count = 0
    
    with ThreadPoolExecutor(max_workers=os.cpu_count()) as executor:
        future_to_file = {executor.submit(_process_single_file, file_info): file_info for file_info in files_to_process}
        
        for future in as_completed(future_to_file):
            processed_count += 1
            file_path, _ = future_to_file[future]
            filename = os.path.basename(file_path)
            
            try:
                key, df, message = future.result()
                if df is not None and not df.empty:
                    all_dataframes[key].append(df)
                log_callback(f"({processed_count}/{len(files_to_process)}) {filename}: {message}")
            except Exception as e:
                log_callback(f"({processed_count}/{len(files_to_process)}) {filename}: Error fatal - {e}")

    final_dataframes = {}
    for key, df_list in all_dataframes.items():
        if df_list:
            final_df = pd.concat(df_list, ignore_index=True)
            log_callback(f"Tabla '{key}' consolidada con {len(final_df)} filas totales.")

            # Agregar columna de pedimento formado para las tablas que la necesitan
            final_df = _agregar_pedimento_formado(final_df, key, log_callback)

            final_dataframes[key] = final_df

    log_callback("✅ Carga del Data Stage completada.")
    return final_dataframes